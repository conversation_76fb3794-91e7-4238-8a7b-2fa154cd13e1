<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-cloud</artifactId>
        <version>1.0.176</version>
    </parent>

    <artifactId>gok-business-auth</artifactId>
    <packaging>jar</packaging>

    <description>gok-platform 认证授权中心，基于 spring security oAuth2</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-upms-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-admin-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!--log-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-log</artifactId>
        </dependency>
        <!--security-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-security</artifactId>
        </dependency>
        <!--feign 依赖-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-feign</artifactId>
        </dependency>
        <!--缓存操作-->
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-data</artifactId>
        </dependency>
<!--        路由控制-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-gray</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.gok.pboot.auth.AuthApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
