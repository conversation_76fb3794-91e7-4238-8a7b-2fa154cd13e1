<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-file</artifactId>
        <version>1.0.176</version>
    </parent>

    <artifactId>gok-business-file-biz</artifactId>
    <packaging>jar</packaging>

    <description>gok-module 通用用户权限管理系统业务处理模块</description>

    <properties>
<!--        <maven.deploy.skip>true</maven.deploy.skip>-->
    </properties>

    <dependencies>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-file-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-log</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security.oauth</groupId>
                    <artifactId>spring-security-oauth2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>

    </dependencies>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <gok-env>dev</gok-env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <gok-env>test</gok-env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <gok-env>prod</gok-env>
            </properties>
        </profile>
    </profiles>

<!--    <build>-->
<!--        <finalName>${project.artifactId}</finalName>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <fork>true</fork>-->
<!--                    <includeSystemScope>true</includeSystemScope>-->
<!--                    <mainClass>com.gok.module.file.FileApplication</mainClass>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>repackage</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->
</project>
