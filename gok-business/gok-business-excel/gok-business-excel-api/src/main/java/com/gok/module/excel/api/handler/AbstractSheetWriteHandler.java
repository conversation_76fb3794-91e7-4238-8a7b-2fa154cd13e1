package com.gok.module.excel.api.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.gok.components.common.user.PigxUser;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.annotation.Sheet;
import com.gok.module.excel.api.aop.DynamicNameAspect;
import com.gok.module.excel.api.config.ExcelConfigProperties;
import com.gok.module.excel.api.converters.LocalDateStringConverter;
import com.gok.module.excel.api.converters.LocalDateTimeStringConverter;
import com.gok.module.excel.api.domain.entity.SysOfficeIoCenter;
import com.gok.module.excel.api.domain.model.LoginUser;
import com.gok.module.excel.api.enhance.WriterBuilderEnhancer;
import com.gok.module.excel.api.enums.FileIOEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.module.excel.api.enums.ModuleEnum;
import com.gok.module.excel.api.enums.ProcessStatusEnum;
import com.gok.module.excel.api.exception.ExcelException;
import com.gok.module.excel.api.head.HeadGenerator;
import com.gok.module.excel.api.head.HeadMeta;
import com.gok.module.excel.api.head.I18nHeaderCellWriteHandler;
import com.gok.module.excel.api.manager.AsyncManager;
import com.gok.module.excel.api.service.FileService;
import com.gok.module.excel.api.service.OfficeIoCenterService;
import com.gok.module.excel.api.utils.Threads;
import com.gok.module.file.entity.SysFile;
import com.gok.pboot.common.security.util.SecurityUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.TimerTask;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020/3/31
 */
@RequiredArgsConstructor
public abstract class AbstractSheetWriteHandler implements SheetWriteHandler, ApplicationContextAware {

    private final OfficeIoCenterService officeIoCenterService;
    private final FileService optFileService;
    private final ExcelConfigProperties configProperties;

    private final ObjectProvider<List<Converter<?>>> converterProvider;

    private final WriterBuilderEnhancer excelWriterBuilderEnhance;

    private ApplicationContext applicationContext;

    @Getter
    @Setter
    @Autowired(required = false)
    private I18nHeaderCellWriteHandler i18nHeaderCellWriteHandler;

    @Override
    public void check(ResponseExcel responseExcel) {
        if (responseExcel.sheets().length == 0) {
            throw new ExcelException("@ResponseExcel sheet 配置不合法");
        }
    }

    @Override
    @SneakyThrows
    public void export(Object o, HttpServletResponse response, ResponseExcel responseExcel) {
        check(responseExcel);
        //包含字段include赋值
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        List<String> includeExcel = (List<String>) Objects.requireNonNull(requestAttributes)
                .getAttribute(DynamicNameAspect.INCLUDE_EXCEL_KEY, RequestAttributes.SCOPE_REQUEST);

        if (!responseExcel.async()) {
            String name = (String) Objects.requireNonNull(requestAttributes)
                    .getAttribute(DynamicNameAspect.EXCEL_NAME_KEY, RequestAttributes.SCOPE_REQUEST);
            String fileName = String.format("%s%s", UriUtils.encode(name, "UTF-8"), responseExcel.suffix().getValue());
            // 根据实际的文件类型找到对应的 contentType
            String contentType = MediaTypeFactory.getMediaType(fileName).map(MediaType::toString)
                    .orElse("application/vnd.ms-excel");
            response.setContentType(contentType);
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", HttpHeaders.CONTENT_DISPOSITION);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            write(o, response, responseExcel, null, includeExcel);
            return;
        }
        PigxUser user = SecurityUtils.getUser();
        response.setContentType("application/vnd.ms-excel");
        // 生成一条状态为“进行中”的导出记录
        Long id = buildDoingFile(responseExcel, user);
        response.setHeader("recordId", String.valueOf(id));

        CountDownLatch countDownLatch = Threads.countDownLatchThreadLocal.get();
        Threads.countDownLatchThreadLocal.remove();

        String excelName = (String) Objects.requireNonNull(requestAttributes)
                .getAttribute(DynamicNameAspect.EXCEL_NAME_KEY, RequestAttributes.SCOPE_REQUEST);

        String finalExcelName = CharSequenceUtil.isBlank(excelName) ? excelName :UriUtils.encode(excelName, "UTF-8");;
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                try {
                    // 等待导出数据处理
                    if (countDownLatch != null) {
                        countDownLatch.await();
                    }
                    response.setHeader("handlerRecordId", String.valueOf(id));
                    response.setHeader("excelName", finalExcelName);
                    write(o, response, responseExcel, user, includeExcel);
                } catch (InterruptedException e) {
                    // 当发生错误时设置导出状态为失败
                    SysOfficeIoCenter failureStatus = new SysOfficeIoCenter();
                    failureStatus.setId(id);
                    failureStatus.setStatus(ProcessStatusEnum.FAIL.getVal());
                    officeIoCenterService.saveOrUpdateOffice(failureStatus);
                    Thread.currentThread().interrupt();
                    e.printStackTrace();
                } catch (Exception e) {
                    // 当发生错误时设置导出状态为失败
                    SysOfficeIoCenter failureStatus = new SysOfficeIoCenter();
                    failureStatus.setId(id);
                    failureStatus.setStatus(ProcessStatusEnum.FAIL.getVal());
                    officeIoCenterService.saveOrUpdateOffice(failureStatus);

                    e.printStackTrace();
                }
            }
        };
        AsyncManager.me().execute(timerTask);
    }

    private Long buildDoingFile(ResponseExcel responseExcel, PigxUser user) {
        // 生成一条状态为“进行中”的导出记录
        FunctionEnum function = responseExcel.functionEnum();
        ModuleEnum module = function.getModule();
        SysOfficeIoCenter officeIoCenter = new SysOfficeIoCenter()
                .setApplicationId(responseExcel.applicationId())
                .setFunc(function.getVal())
                .setFunctionName(function.getKey())
                .setModule(module.getVal())
                .setModuleName(module.getKey())
                .setType(FileIOEnum.EXPORT.getVal())
                .setStatus(ProcessStatusEnum.DOING.getVal());
        if (user!=null) {
            officeIoCenter.setCreateBy(user.getUsername());
            officeIoCenter.setUpdateBy(user.getUsername());
        }
        return officeIoCenterService.saveOrUpdateOffice(officeIoCenter);
    }

    /**
     * 通用的获取ExcelWriter方法
     *
     * @param response      HttpServletResponse
     * @param responseExcel ResponseExcel注解
     * @param includeExcel  动态表头
     * @return ExcelWriter
     */
    @SneakyThrows
    public ExcelWriter getExcelWriter(HttpServletResponse response,
                                      ResponseExcel responseExcel,
                                      List<String> includeExcel,
                                      Class<?> dataClass) {

        ExcelWriterBuilder writerBuilder = disposeWriterBuilder(responseExcel, includeExcel, dataClass, response.getOutputStream());

        String templatePath = configProperties.getTemplatePath();

        if (StringUtils.hasText(responseExcel.template())) {
            ClassPathResource classPathResource = new ClassPathResource(templatePath + File.separator + responseExcel.template());
            InputStream inputStream = classPathResource.getInputStream();
            writerBuilder.withTemplate(inputStream);
        }

        writerBuilder = excelWriterBuilderEnhance.enhanceExcel(writerBuilder, response, responseExcel, templatePath);

        return writerBuilder.build();
    }


    /**
     * 通用的获取ExcelWriter方法
     *
     * @param responseExcel ResponseExcel注解
     * @param includeExcel  动态表头
     * @return ExcelWriter
     */
    @SneakyThrows
    public ExcelWriter getExcelWriter(ByteArrayOutputStream outputStream,
                                      ResponseExcel responseExcel,
                                      List<String> includeExcel,
                                      Class<?> dataClass) {

        ExcelWriterBuilder writerBuilder = disposeWriterBuilder(responseExcel, includeExcel, dataClass, outputStream);

        String templatePath = configProperties.getTemplatePath();

        if (StringUtils.hasText(responseExcel.template())) {
            ClassPathResource classPathResource = new ClassPathResource(templatePath + File.separator + responseExcel.template());
            InputStream inputStream = classPathResource.getInputStream();
            writerBuilder.withTemplate(inputStream);
        }
        return writerBuilder.build();
    }

    private ExcelWriterBuilder disposeWriterBuilder(ResponseExcel responseExcel, List<String> includeExcel, Class<?> dataClass, OutputStream outputStream) {
        ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream)
                .registerConverter(new LongStringConverter())
                .registerConverter(LocalDateStringConverter.INSTANCE)
                .registerConverter(LocalDateTimeStringConverter.INSTANCE)
                .autoCloseStream(true)
                .excelType(responseExcel.suffix())
                .inMemory(responseExcel.inMemory());
        // 密码
        String pwd = responseExcel.password();
        if (StringUtils.hasText(pwd)) {
            writerBuilder.password(pwd);
        }
        // 包含的字段
        if (responseExcel.include().length != 0) {
            writerBuilder.includeColumnFieldNames(Arrays.asList(responseExcel.include()));
        }
        // 动态表头字段
        if (responseExcel.dynamicHeader() && ArrayUtil.isNotEmpty(includeExcel)) {
            writerBuilder.includeColumnFieldNames(includeExcel);
            writerBuilder.orderByIncludeColumn(true);
        }
        // 排除的字段
        if (responseExcel.exclude().length != 0) {
            writerBuilder.excludeColumnFieldNames(Arrays.asList(responseExcel.exclude()));
        }
        // 注册自定义的WriteHandler
        for (Class<? extends WriteHandler> clazz : responseExcel.writeHandler()) {
            writerBuilder.registerWriteHandler(BeanUtils.instantiateClass(clazz));
        }
        // 合并相同值的列
        if (responseExcel.mergeSameColumn()) {
            writerBuilder.registerWriteHandler(new MergeCellByIndexStrategyHandler());
        }
        // 处理Excel下拉列表
        if (responseExcel.selector()) {
            writerBuilder.registerWriteHandler(new SelectedSheetWriteHandler(dataClass));
        }

        // 开启国际化头信息处理
        if (responseExcel.i18nHeader() && i18nHeaderCellWriteHandler != null) {
            writerBuilder.registerWriteHandler(i18nHeaderCellWriteHandler);
        }

        // 自定义注入的转换器
        registerCustomConverter(writerBuilder);

        // 自定义注入的转换器
        for (Class<? extends Converter> clazz : responseExcel.converter()) {
            writerBuilder.registerConverter(BeanUtils.instantiateClass(clazz));
        }

        return writerBuilder;
    }

    /**
     * 上传文件
     */
    public void uploadFile(MultipartFile file, FunctionEnum function, PigxUser user) {
        SysFile sysFile = optFileService.uploadOptFile(file);
        SysOfficeIoCenter officeIoCenter = new SysOfficeIoCenter();
        ModuleEnum module = function.getModule();
        officeIoCenter.setDocId(sysFile.getId())
                .setFunc(function.getVal())
                .setFunctionName(function.getKey())
                .setModule(module.getVal())
                .setModuleName(module.getKey())
                .setFileName(sysFile.getFileName())
                .setType(FileIOEnum.EXPORT.getVal())
                .setStatus(ProcessStatusEnum.SUCCESS.getVal())
                .setFileSize(sysFile.getFileSize().toString());
        if (user!=null) {
            officeIoCenter.setCreateBy(user.getUsername());
            officeIoCenter.setUpdateBy(user.getUsername());
        }
        officeIoCenterService.saveOrUpdateOffice(officeIoCenter);
    }

    /**
     * 上传文件
     */
    public void uploadFile(MultipartFile file, Long id) {
        SysFile sysFile = optFileService.uploadOptFile(file);
        SysOfficeIoCenter officeIoCenter = new SysOfficeIoCenter();
        officeIoCenter.setId(id);
        officeIoCenter.setDocId(sysFile.getId());
        officeIoCenter.setFileName(sysFile.getFileName());
        officeIoCenter.setStatus(ProcessStatusEnum.SUCCESS.getVal());
        officeIoCenter.setFileSize(sysFile.getFileSize().toString());
        officeIoCenterService.saveOrUpdateOffice(officeIoCenter);
    }

    /**
     * 上传文件
     */
    public void uploadFile(MultipartFile file, FunctionEnum function, LoginUser user, Long applicationId) {
        SysFile sysFile = optFileService.uploadOptFile(file);
        SysOfficeIoCenter officeIoCenter = new SysOfficeIoCenter();
        officeIoCenter.setApplicationId(applicationId);
        officeIoCenter.setDocId(sysFile.getId());
        officeIoCenter.setFunc(function.getVal());
        officeIoCenter.setFunctionName(function.getKey());
        ModuleEnum module = function.getModule();
        officeIoCenter.setModule(module.getVal());
        officeIoCenter.setModuleName(module.getKey());
        officeIoCenter.setFileName(sysFile.getFileName());
        officeIoCenter.setType(FileIOEnum.EXPORT.getVal());
        officeIoCenter.setStatus(ProcessStatusEnum.SUCCESS.getVal());
        officeIoCenter.setFileSize(sysFile.getFileSize().toString());
        if (user!=null) {
            officeIoCenter.setCreateBy(user.getUsername());
            officeIoCenter.setUpdateBy(user.getUsername());
        }
        officeIoCenterService.saveOrUpdateOffice(officeIoCenter);
    }

    /**
     * 自定义注入转换器 如果有需要，子类自己重写
     *
     * @param builder ExcelWriterBuilder
     */
    public void registerCustomConverter(ExcelWriterBuilder builder) {
        converterProvider.ifAvailable(converters -> converters.forEach(builder::registerConverter));
    }

    /**
     * 获取 WriteSheet 对象
     *
     * @param sheet                 sheet annotation info
     * @param dataClass             数据类型
     * @param template              模板
     * @param bookHeadEnhancerClass 自定义头处理器
     * @return WriteSheet
     */
    public WriteSheet sheet(Sheet sheet, Class<?> dataClass, String template,
                            Class<? extends HeadGenerator> bookHeadEnhancerClass) {

        // Sheet 编号和名称
        Integer sheetNo = sheet.sheetNo() >= 0 ? sheet.sheetNo() : null;
        String sheetName = sheet.sheetName();

        // 是否模板写入
        ExcelWriterSheetBuilder writerSheetBuilder = StringUtils.hasText(template) ? EasyExcel.writerSheet(sheetNo)
                : EasyExcel.writerSheet(sheetNo, sheetName);

        // 头信息增强 1. 优先使用 sheet 指定的头信息增强 2. 其次使用 @ResponseExcel 中定义的全局头信息增强
        Class<? extends HeadGenerator> headGenerateClass = null;
        if (isNotInterface(sheet.headGenerateClass())) {
            headGenerateClass = sheet.headGenerateClass();
        } else if (isNotInterface(bookHeadEnhancerClass)) {
            headGenerateClass = bookHeadEnhancerClass;
        }
        // 定义头信息增强则使用其生成头信息，否则使用 dataClass 来自动获取
        if (headGenerateClass != null) {
            fillCustomHeadInfo(dataClass, bookHeadEnhancerClass, writerSheetBuilder);
        } else if (dataClass != null) {
            writerSheetBuilder.head(dataClass);
            if (sheet.excludes().length > 0) {
                writerSheetBuilder.excludeColumnFiledNames(Arrays.asList(sheet.excludes()));
            }
            if (sheet.includes().length > 0) {
                writerSheetBuilder.includeColumnFiledNames(Arrays.asList(sheet.includes()));
            }
        }

        // sheetBuilder 增强
        writerSheetBuilder = excelWriterBuilderEnhance.enhanceSheet(writerSheetBuilder, sheetNo, sheetName, dataClass,
                template, headGenerateClass);

        return writerSheetBuilder.build();
    }

    private void fillCustomHeadInfo(Class<?> dataClass, Class<? extends HeadGenerator> headEnhancerClass,
                                    ExcelWriterSheetBuilder writerSheetBuilder) {
        HeadGenerator headGenerator = this.applicationContext.getBean(headEnhancerClass);
        Assert.notNull(headGenerator, "The header generated bean does not exist.");
        HeadMeta head = headGenerator.head(dataClass);
        writerSheetBuilder.head(head.getHead());
        writerSheetBuilder.excludeColumnFiledNames(head.getIgnoreHeadFields());
    }

    /**
     * 是否为Null Head Generator
     *
     * @param headGeneratorClass
     * @return true 已指定 false 未指定(默认值)
     */
    private boolean isNotInterface(Class<? extends HeadGenerator> headGeneratorClass) {
        return !Modifier.isInterface(headGeneratorClass.getModifiers());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
//        this.remoteOfficeIoCenterService= SpringUtil.getBean(RemoteOfficeIoCenterService.class);
    }

}
