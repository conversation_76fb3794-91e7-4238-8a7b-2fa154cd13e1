package com.gok.module.excel.api.util;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.List;
import java.util.Map;

/**
 * Excel字段顺序控制工具类
 * 用于动态设置@ExcelProperty注解的index属性，实现字段顺序控制
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Slf4j
public class ExcelFieldOrderUtil {

    /**
     * 根据字段名称列表动态设置实体类字段的导出顺序
     * 
     * @param clazz 实体类
     * @param includeFieldNames 需要导出的字段名称列表（按顺序）
     * @param <T> 泛型类型
     */
    public static <T> void setFieldOrder(Class<T> clazz, List<String> includeFieldNames) {
        if (includeFieldNames == null || includeFieldNames.isEmpty()) {
            return;
        }

        try {
            for (int i = 0; i < includeFieldNames.size(); i++) {
                String fieldName = includeFieldNames.get(i);
                Field field = clazz.getDeclaredField(fieldName);
                
                if (field != null) {
                    ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                    if (excelProperty != null) {
                        setExcelPropertyIndex(excelProperty, i);
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置Excel字段顺序失败", e);
        }
    }

    /**
     * 通过反射修改@ExcelProperty注解的index属性
     * 
     * @param excelProperty ExcelProperty注解实例
     * @param index 要设置的索引值
     */
    private static void setExcelPropertyIndex(ExcelProperty excelProperty, int index) {
        try {
            InvocationHandler invocationHandler = Proxy.getInvocationHandler(excelProperty);
            Field memberValuesField = invocationHandler.getClass().getDeclaredField("memberValues");
            ReflectionUtils.makeAccessible(memberValuesField);
            
            @SuppressWarnings("unchecked")
            Map<String, Object> memberValues = (Map<String, Object>) memberValuesField.get(invocationHandler);
            memberValues.put("index", index);
            
        } catch (Exception e) {
            log.error("设置ExcelProperty index属性失败", e);
        }
    }

    /**
     * 重置字段顺序（清除之前设置的index）
     * 
     * @param clazz 实体类
     * @param fieldNames 字段名称列表
     * @param <T> 泛型类型
     */
    public static <T> void resetFieldOrder(Class<T> clazz, List<String> fieldNames) {
        if (fieldNames == null || fieldNames.isEmpty()) {
            return;
        }

        try {
            for (String fieldName : fieldNames) {
                Field field = clazz.getDeclaredField(fieldName);
                
                if (field != null) {
                    ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                    if (excelProperty != null) {
                        setExcelPropertyIndex(excelProperty, -1);
                    }
                }
            }
        } catch (Exception e) {
            log.error("重置Excel字段顺序失败", e);
        }
    }
}
