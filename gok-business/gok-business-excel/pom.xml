<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business</artifactId>
        <version>1.0.176</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>gok-business-excel</artifactId>

    <modules>
        <module>gok-business-excel-biz</module>
        <module>gok-business-excel-api</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-data</artifactId>
        </dependency>
        <!--util组件-->
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
        </dependency>
    </dependencies>

</project>
