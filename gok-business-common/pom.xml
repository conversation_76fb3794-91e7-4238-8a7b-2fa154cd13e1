<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-cloud</artifactId>
        <version>1.0.176</version>
    </parent>

    <groupId>com.gok.business.cloud</groupId>
    <artifactId>gok-business-common</artifactId>
    <version>1.0.176</version>
    <packaging>pom</packaging>

    <description>公共聚合模块</description>

    <properties>
        <security.oauth.version>2.3.6.RELEASE</security.oauth.version>
        <openfeign.version>11.8</openfeign.version>
    </properties>

    <modules>
        <module>gok-business-common-bom</module>
        <module>gok-business-common-core</module>
        <module>gok-business-common-feign</module>
        <module>gok-business-common-gateway</module>
        <module>gok-business-common-gray</module>
        <module>gok-business-common-idempotent</module>
        <module>gok-business-common-log</module>
        <module>gok-business-common-security</module>
        <module>gok-business-common-sentinel</module>
        <module>gok-business-common-transaction</module>
        <module>gok-business-common-websocket</module>
        <module>gok-business-common-xss</module>
        <module>gok-business-common-elastic</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--稳定版本，替代spring security bom内置-->
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${security.oauth.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
