<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-common</artifactId>
        <version>1.0.176</version>
    </parent>

    <artifactId>gok-business-common-log</artifactId>
    <packaging>jar</packaging>

    <description>gok-platform 日志服务</description>


    <dependencies>
        <!--工具类核心包-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!--http 工具类-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
        </dependency>
        <!--UPMS接口模块-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-upms-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-admin-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!--安全依赖获取上下文信息-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
            <version>1.0.40</version>
        </dependency>
    </dependencies>
</project>
