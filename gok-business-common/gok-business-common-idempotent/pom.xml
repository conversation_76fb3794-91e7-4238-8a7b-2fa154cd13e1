<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-common</artifactId>
        <version>1.0.176</version>
    </parent>

    <artifactId>gok-business-common-idempotent</artifactId>
    <packaging>jar</packaging>

    <description>gok-platform 幂等插件</description>

    <properties>
        <redisson.version>3.16.7</redisson.version>
    </properties>

    <dependencies>
        <!--aop-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
<!--            <version>${spring-boot.version}</version>-->
            <scope>provided</scope>
        </dependency>

        <!--redisson-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>

        <!--servlet 依赖-->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>4.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
<!--            <version>5.3.15</version>-->
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
