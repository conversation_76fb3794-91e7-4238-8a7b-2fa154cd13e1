<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-common</artifactId>
        <version>1.0.176</version>
    </parent>

    <artifactId>gok-business-common-security</artifactId>
    <packaging>jar</packaging>

    <description>gok-platform 安全工具类</description>


    <dependencies>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
            <version>1.0.40</version>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>org.springframework.security.oauth.boot</groupId>
            <artifactId>spring-security-oauth2-autoconfigure</artifactId>
        </dependency>
        <!--feign-->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
            <version>${openfeign.version}</version>
        </dependency>
        <!--aop-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>1.9.7</version>
        </dependency>
        <!--缓存依赖-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <!--upms fegin 调用相关工具类-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-upms-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-admin-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <!--Feign依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-data</artifactId>
            <version>1.0.40</version>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-upms-api</artifactId>
            <version>1.1.139</version>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-upms-out</artifactId>
            <version>1.1.205</version>
        </dependency>
    </dependencies>
</project>
