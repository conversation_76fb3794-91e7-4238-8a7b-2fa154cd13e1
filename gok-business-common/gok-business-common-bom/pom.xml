<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.gok.components</groupId>
        <artifactId>gok-components-bom</artifactId>
        <version>1.0.40</version>
        <relativePath/>
    </parent>

    <groupId>com.gok.business.cloud</groupId>
    <artifactId>gok-business-common-bom</artifactId>
    <version>${gok-base-common.version}</version>
    <packaging>pom</packaging>

    <properties>
        <jasypt.version>3.0.3</jasypt.version>
        <security.oauth.version>2.3.6.RELEASE</security.oauth.version>
        <gok-base-common.version>1.0.176</gok-base-common.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
        </dependency>
        <!--bootstrap 启动器-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--jasypt配置文件加解密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-core</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-feign</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-gateway</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-gray</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-idempotent</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-log</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-security</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-sentinel</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-transaction</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-websocket</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-xss</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>
            <!--稳定版本，替代spring security bom内置-->
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${security.oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gok.business.cloud</groupId>
                <artifactId>gok-business-common-elastic</artifactId>
                <version>${gok-base-common.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>
