<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-common</artifactId>
        <version>1.0.176</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>gok-business-common-sentinel</artifactId>
    <description>sentinel服务降级熔断、限流组件</description>
    <dependencies>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
<!--            <version>2021.1</version>-->
        </dependency>
        <!--feign 依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
<!--            <version>3.1.1</version>-->
        </dependency>
        <!-- okhttp 扩展 -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
            <version>11.8</version>
        </dependency>
        <!--oauth server 依赖-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
<!--            <version>5.6.1</version>-->
        </dependency>
    </dependencies>
</project>
